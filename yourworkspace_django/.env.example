# Django Configuration
SECRET_KEY=your-secret-key-here-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database Configuration
DATABASE_URL=postgresql://yourworkspace_user:your_password@localhost:5432/yourworkspace
DB_PASSWORD=your_database_password

# Redis Configuration
REDIS_URL=redis://localhost:6379/1

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Error Tracking (Optional)
SENTRY_DSN=https://<EMAIL>/project-id

# SSL Certificate paths (for production)
SSL_CERT_PATH=/path/to/your/cert.pem
SSL_KEY_PATH=/path/to/your/key.pem
