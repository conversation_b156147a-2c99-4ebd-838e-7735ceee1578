后端修改须知：整合“当前焦点”至“待办事项” (V3架构)致：后端开发负责人发件人：[您的名字/团队名字]日期：{YYYY-MM-DD} (请替换为当前日期)主题：为配合前端V3架构调整，“当前焦点”功能整合至“待办事项”模块的后端修改说明您好！为了优化应用的功能结构和用户体验，前端架构进行了调整（V3版本）。其中最核心的变动之一是将原有的独立“当前焦点 (Current Focus)”模块整合到“待办事项 (To-Do List)”模块中。在此新架构下，“当前焦点”不再作为一种独立的数据类型或通过专门的API端点进行管理。取而代之的是，任何一个“待办事项”都可以被标记为“当前焦点”。这通常通过在待办事项数据模型中增加一个布尔类型的标志位（例如 is_current_focus）来实现。为配合前端的这一重要调整，后端需要进行以下相应的修改：1. 数据库层面调整todo_items 表 (或类似的待办事项数据表)：需要新增一个布尔类型的字段，建议命名为 is_current_focus。该字段应有默认值，通常为 false (或 0)。此字段用于标识一个待办事项是否被用户选为“当前焦点”。2. “待办事项”相关 API (/api/v1/todo/todos) 的调整创建待办事项 (POST /api/v1/todo/todos):在创建新的待办事项时，is_current_focus 字段应默认为 false。前端在创建新待办时，通常不会传递此标志。获取待办事项 (单个及列表):GET /api/v1/todo/todos (获取所有待办事项)GET /api/v1/todo/todos/<int:todo_id> (获取单个待办事项)这两个API的返回数据中，必须包含每个待办事项的 is_current_focus 字段及其当前值。前端需要此信息来正确显示哪些是当前焦点任务。更新待办事项 (PUT /api/v1/todo/todos/<int:todo_id>):这是本次调整中最为关键的API修改。此API的请求体 (payload) 中，必须能够接收并处理 is_current_focus 字段 (布尔类型)。当用户在前端将一个待办事项标记为“当前焦点”或取消标记时，前端会调用此API，并在请求体中发送更新后的 is_current_focus 值。后端需要相应地更新数据库中该待办事项的此字段。此API也应继续支持更新待办事项的其他字段（如 title, description, status 等）。3. 废弃/移除原有的“当前焦点”专属API端点根据之前的API文档 (prompts/frontend/phase1.txt 第3.3节)，我们有以下用于独立管理“当前焦点”的API端点：GET /api/v1/anchor/current_focusPOST /api/v1/anchor/current_focusGET /api/v1/anchor/current_focus/<int:focus_id>PUT /api/v1/anchor/current_focus/<int:focus_id>DELETE /api/v1/anchor/current_focus/<int:focus_id>由于“当前焦点”现在已成为“待办事项”的一个属性，上述这些专属的“当前焦点”API端点将不再被前端使用，建议进行废弃或移除处理。所有与“当前焦点”相关的操作都将通过修改待办事项的 is_current_focus 标志并调用 PUT /api/v1/todo/todos/<int:todo_id> 来完成。4. (可选) 关于“当前焦点”数量限制的考量前端设计中可能会有对“当前焦点”数量的限制（例如，用户最多只能将3个待办事项标记为当前焦点）。目前，这个限制逻辑主要由前端在 todoStore.ts 中处理。如果后端也需要强制执行此限制（例如，防止通过直接API调用绕过前端逻辑），则在 PUT /api/v1/todo/todos/<int:todo_id> 更新 is_current_focus 为 true 时，可能需要进行相应的校验。但这可以作为后续的增强功能。总结此次架构调整旨在简化数据模型，消除功能冗余，并提升用户体验。核心是将“当前焦点”的概念融入到“待办事项”中。后端的主要工作是确保“待办事项”的数据模型和API支持 is_current_focus 标志的读取和更新，并移除旧的、独立的“当前焦点”管理接口。请在完成上述修改后通知前端团队，以便我们进行联调测试。如有任何疑问，请随时与我们沟通。感谢您的配合！
