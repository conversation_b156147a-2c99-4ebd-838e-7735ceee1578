Flask Backend API Manual - Phase 1 (V3 Architecture)This document outlines the available API endpoints for the "Personal Workspace & Anchor Point" application, Phase 1, reflecting the V3 architecture changes (integration of Current Focus into To-Do Items).Base URL: All API endpoints are prefixed with /api/v1. Assuming the development server runs on http://localhost:5000, the full base URL would be http://localhost:5000/api/v1.Authentication:Most endpoints require authentication using JSON Web Tokens (JWT).Clients must first obtain an access_token and refresh_token from the /auth/login endpoint.For protected endpoints, the client must send the access_token in the Authorization header using the Bearer scheme: Authorization: Bearer <YOUR_ACCESS_TOKEN>.Some endpoints may require a refresh_token instead (specified below).1. Authentication (/auth)Endpoints related to user registration, login, and token management.1.1. Register UserEndpoint: POST /auth/registerDescription: Creates a new user account.Auth Required: NoRequest Body (JSON):{
  "username": "string (required, unique)",
  "email": "string (required, unique, valid email format)",
  "password": "string (required, min length recommended)"
}
Success Response (201 Created):{
  "message": "User registered successfully",
  "user": {
    "id": integer,
    "username": "string",
    "email": "string",
    "created_at": "string (ISO 8601 format, e.g., YYYY-MM-DDTHH:MM:SS.ffffffZ)"
  }
}
Error Responses:400 Bad Request: Missing fields, invalid data types.409 Conflict: Username or email already exists.500 Internal Server Error: Database error during creation.1.2. Login UserEndpoint: POST /auth/loginDescription: Authenticates a user and returns JWT access and refresh tokens.Auth Required: NoRequest Body (JSON):{
  "email": "string (required)",
  "password": "string (required)"
}
Success Response (200 OK):{
  "access_token": "string (JWT)",
  "refresh_token": "string (JWT)"
}
Error Responses:400 Bad Request: Missing fields.401 Unauthorized: Invalid email or password.1.3. Get User Profile (Me)Endpoint: GET /auth/meDescription: Retrieves basic profile information for the currently authenticated user.Auth Required: Yes (Access Token)Request Body: NoneSuccess Response (200 OK):{
  "id": integer,
  "username": "string",
  "email": "string",
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
Error Responses:401 Unauthorized: Missing, invalid, or expired token.404 Not Found: User associated with token not found (rare).1.4. Refresh Access TokenEndpoint: POST /auth/refreshDescription: Issues a new, non-fresh access token using a valid refresh token.Auth Required: Yes (Refresh Token - use refresh token in Authorization: Bearer <refresh_token>)Request Body: NoneSuccess Response (200 OK):{
  "access_token": "string (JWT - non-fresh)"
}
Error Responses:401 Unauthorized: Missing, invalid, expired, or revoked refresh token.422 Unprocessable Entity: If an access token is used instead of a refresh token.1.5. Logout (Revoke Access Token)Endpoint: POST /auth/logoutDescription: Revokes the access token used to make the request by adding its JTI to the blocklist. The client should also discard the token.Auth Required: Yes (Access Token)Request Body: NoneSuccess Response (200 OK):{
  "message": "Access token revoked. User logged out."
}
Error Responses:401 Unauthorized: Missing, invalid, expired, or already revoked token.500 Internal Server Error: Error adding token to blocklist.1.6. Logout Refresh (Revoke Refresh Token)Endpoint: POST /auth/logout-refreshDescription: Revokes the refresh token used to make the request by adding its JTI to the blocklist. Use this if you want explicit refresh token revocation.Auth Required: Yes (Refresh Token)Request Body: NoneSuccess Response (200 OK):{
  "message": "Refresh token revoked."
}
Error Responses:401 Unauthorized: Missing, invalid, expired, or already revoked refresh token.422 Unprocessable Entity: If an access token is used.500 Internal Server Error: Error adding token to blocklist.1.7. Change PasswordEndpoint: POST /auth/change-passwordDescription: Allows the user to change their password. Requires a "fresh" access token (obtained directly from login).Auth Required: Yes (Fresh Access Token)Request Body (JSON):{
  "new_password": "string (required)"
}
Success Response (200 OK):{
  "message": "Password updated successfully."
}
Error Responses:400 Bad Request: Missing new_password.401 Unauthorized: Missing, invalid, expired, or non-fresh token.404 Not Found: User not found.500 Internal Server Error: Error saving password.2. To-Do List (/todo)Endpoints for managing user-specific to-do items. All endpoints require authentication (Access Token). The is_current_focus field indicates if a to-do item is marked as a "Current Focus".2.1. Get All To-Do ItemsEndpoint: GET /todo/todosDescription: Retrieves a list of all to-do items for the authenticated user. Items where is_current_focus is true are listed first, then by creation date descending.Auth Required: YesRequest Body: NoneSuccess Response (200 OK): An array of to-do item objects.[
  {
    "id": integer,
    "user_id": integer,
    "title": "string",
    "description": "string | null",
    "due_date": "string (YYYY-MM-DD) | null",
    "status": "string ('pending', 'in_progress', 'completed', 'deferred')",
    "priority": "string ('low', 'medium', 'high')",
    "is_current_focus": boolean, // True if item is a current focus
    "created_at": "string (ISO 8601)",
    "updated_at": "string (ISO 8601)",
    "completed_at": "string (ISO 8601) | null"
  },
  // ... more items
]
Error Responses:401 Unauthorized.2.2. Create To-Do ItemEndpoint: POST /todo/todosDescription: Creates a new to-do item for the authenticated user. is_current_focus defaults to false.Auth Required: YesRequest Body (JSON):{
  "title": "string (required)",
  "description": "string (optional)",
  "due_date": "string (YYYY-MM-DD, optional)",
  "status": "string (optional, default: 'pending', allowed: 'pending', 'in_progress', 'completed', 'deferred')",
  "priority": "string (optional, default: 'medium', allowed: 'low', 'medium', 'high')"
  // "is_current_focus" is typically not sent on creation; managed via PUT.
}
Success Response (201 Created): The newly created to-do item object (see format in 2.1).Error Responses:400 Bad Request: Missing title, invalid data types or formats (date, status, priority).401 Unauthorized.500 Internal Server Error.2.3. Get Specific To-Do ItemEndpoint: GET /todo/todos/<int:todo_id>Description: Retrieves a single to-do item by its ID.Auth Required: YesRequest Body: NoneSuccess Response (200 OK): The requested to-do item object (see format in 2.1).Error Responses:401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: To-do item with the given ID does not exist.2.4. Update To-Do ItemEndpoint: PUT /todo/todos/<int:todo_id>Description: Updates an existing to-do item. Only fields provided in the body are updated. This endpoint is used to update any field, including toggling is_current_focus.Auth Required: YesRequest Body (JSON): Include any fields to update.{
  "title": "string",
  "description": "string | null",
  "due_date": "string (YYYY-MM-DD) | null",
  "status": "string ('pending', 'in_progress', 'completed', 'deferred')",
  "priority": "string ('low', 'medium', 'high')",
  "is_current_focus": boolean // To mark/unmark as current focus
}
Success Response (200 OK): The updated to-do item object (see format in 2.1).Error Responses:400 Bad Request: Invalid data types or formats.401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: To-do item not found.500 Internal Server Error.2.5. Delete To-Do ItemEndpoint: DELETE /todo/todos/<int:todo_id>Description: Deletes a specific to-do item.Auth Required: YesRequest Body: NoneSuccess Response (204 No Content): Empty body.Error Responses:401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: To-do item not found.500 Internal Server Error.3. Personal Anchor Overview (/anchor)Endpoints for managing the user's profile, achievements, and future plans. All endpoints require authentication (Access Token). The "Current Focus" section is now managed as part of To-Do Items.3.1. Profile (/anchor/profile)3.1.1. Get User ProfileEndpoint: GET /anchor/profileDescription: Retrieves the user's professional profile information. Creates an empty profile if one doesn't exist for the user.Auth Required: YesRequest Body: NoneSuccess Response (200 OK):{
  "user_id": integer,
  "professional_title": "string | null",
  "one_liner_bio": "string | null",
  "created_at": "string (ISO 8601)",
  "updated_at": "string (ISO 8601)"
}
Error Responses:401 Unauthorized.404 Not Found: User not found.500 Internal Server Error: Failed to create missing profile.3.1.2. Update User ProfileEndpoint: PUT /anchor/profileDescription: Updates the user's professional profile information. Only fields provided are updated.Auth Required: YesRequest Body (JSON):{
  "professional_title": "string | null",
  "one_liner_bio": "string | null"
}
Success Response (200 OK): The updated profile object (see format in 3.1.1). Can also return {"message": "No relevant profile fields provided..."} if the body contains no updatable fields.Error Responses:400 Bad Request: Invalid data types, empty JSON body.401 Unauthorized.404 Not Found: User not found.500 Internal Server Error.3.2. Achievements (/anchor/achievements)3.2.1. Get All AchievementsEndpoint: GET /anchor/achievementsDescription: Retrieves all achievements for the user, ordered by date_achieved desc (nulls last), then created_at desc.Auth Required: YesRequest Body: NoneSuccess Response (200 OK): An array of achievement objects.[
  {
    "id": integer,
    "user_id": integer,
    "title": "string",
    "description": "string | null",
    "quantifiable_results": "string | null",
    "core_skills_json": ["string", ...], // list of strings, defaults to []
    "date_achieved": "string (YYYY-MM-DD) | null",
    "created_at": "string (ISO 8601)",
    "updated_at": "string (ISO 8601)"
  },
  // ... more items
]
Error Responses: 401 Unauthorized.3.2.2. Create AchievementEndpoint: POST /anchor/achievementsDescription: Creates a new achievement.Auth Required: YesRequest Body (JSON):{
  "title": "string (required)",
  "description": "string (optional)",
  "quantifiable_results": "string (optional)",
  "core_skills_json": ["string", ...] (optional, must be list of strings if provided),
  "date_achieved": "string (YYYY-MM-DD, optional)"
}
Success Response (201 Created): The newly created achievement object (see format in 3.2.1).Error Responses:400 Bad Request: Missing title, invalid data types/formats (skills list, date).401 Unauthorized.500 Internal Server Error.3.2.3. Get Specific AchievementEndpoint: GET /anchor/achievements/<int:achievement_id>Description: Retrieves a single achievement by ID.Auth Required: YesRequest Body: NoneSuccess Response (200 OK): The requested achievement object (see format in 3.2.1).Error Responses:401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: Achievement not found.3.2.4. Update AchievementEndpoint: PUT /anchor/achievements/<int:achievement_id>Description: Updates an existing achievement.Auth Required: YesRequest Body (JSON): Include any fields to update (same fields as POST, all optional here).Success Response (200 OK): The updated achievement object (see format in 3.2.1).Error Responses:400 Bad Request: Invalid data types/formats.401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: Achievement not found.500 Internal Server Error.3.2.5. Delete AchievementEndpoint: DELETE /anchor/achievements/<int:achievement_id>Description: Deletes a specific achievement.Auth Required: YesRequest Body: NoneSuccess Response (204 No Content): Empty body.Error Responses:401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: Achievement not found.500 Internal Server Error.3.3. Future Plans (/anchor/future_plans)3.3.1. Get All Future PlansEndpoint: GET /anchor/future_plansDescription: Retrieves all future plans for the user, ordered by target_date asc (nulls last), then created_at desc.Auth Required: YesRequest Body: NoneSuccess Response (200 OK): An array of future plan objects.[
  {
    "id": integer,
    "user_id": integer,
    "goal_type": "string | null",
    "description": "string",
    "target_date": "string (YYYY-MM-DD) | null",
    "status": "string ('active', 'achieved', 'deferred', 'abandoned')",
    "created_at": "string (ISO 8601)",
    "updated_at": "string (ISO 8601)"
  },
  // ... more items
]
Error Responses: 401 Unauthorized.3.3.2. Create Future PlanEndpoint: POST /anchor/future_plansDescription: Creates a new future plan.Auth Required: YesRequest Body (JSON):{
  "description": "string (required)",
  "goal_type": "string (optional, max 50 chars)",
  "target_date": "string (YYYY-MM-DD, optional)",
  "status": "string (optional, default: 'active', allowed: 'active', 'achieved', 'deferred', 'abandoned')"
}
Success Response (201 Created): The newly created plan object (see format in 3.3.1).Error Responses:400 Bad Request: Missing description, invalid data types/formats (date, status).401 Unauthorized.500 Internal Server Error.3.3.3. Get Specific Future PlanEndpoint: GET /anchor/future_plans/<int:plan_id>Description: Retrieves a single future plan by ID.Auth Required: YesRequest Body: NoneSuccess Response (200 OK): The requested plan object (see format in 3.3.1).Error Responses:401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: Plan not found.3.3.4. Update Future PlanEndpoint: PUT /anchor/future_plans/<int:plan_id>Description: Updates an existing future plan.Auth Required: YesRequest Body (JSON): Include any fields to update (same fields as POST, all optional here).Success Response (200 OK): The updated plan object (see format in 3.3.1).Error Responses:400 Bad Request: Invalid data types/formats.401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: Plan not found.500 Internal Server Error.3.3.5. Delete Future PlanEndpoint: DELETE /anchor/future_plans/<int:plan_id>Description: Deletes a specific future plan.Auth Required: YesRequest Body: NoneSuccess Response (204 No Content): Empty body.Error Responses:401 Unauthorized.403 Forbidden: User does not own this item.404 Not Found: Plan not found.500 Internal Server Error.This manual should now be complete and accurately reflect the V3 architecture for Phase 1.
